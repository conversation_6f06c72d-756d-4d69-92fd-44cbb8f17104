/* Glassmorphic Login Page Styles */

/* 修复滑动过头白边问题 - 全局设置 */
html {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  /* 防止过度滚动显示白色背景 */
  overscroll-behavior: none;
  -webkit-overscroll-behavior: none;
  /* 设置全局背景色，确保滑动时不显示白色 */
  background: linear-gradient(135deg, 
    rgb(15, 23, 42) 0%, 
    rgb(30, 58, 138) 35%, 
    rgb(91, 33, 182) 100%
  );
  /* 移动端弹性滚动控制 */
  -webkit-overflow-scrolling: touch;
  /* 禁用拖拽选择以避免滚动冲突 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  /* 防止过度滚动显示白色背景 */
  overscroll-behavior: none;
  -webkit-overscroll-behavior: none;
  /* 设置body背景色，确保滑动时不显示白色 */
  background: linear-gradient(135deg, 
    rgb(15, 23, 42) 0%, 
    rgb(30, 58, 138) 35%, 
    rgb(91, 33, 182) 100%
  );
  /* 移动端弹性滚动控制 */
  -webkit-overflow-scrolling: touch;
  /* 避免内容缩放 */
  -webkit-text-size-adjust: 100%;
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: transparent;
}

/* 根元素背景色设置 */
#root {
  min-height: 100vh;
  height: 100%;
  /* 设置根元素背景色，确保滑动时不显示白色 */
  background: linear-gradient(135deg, 
    rgb(15, 23, 42) 0%, 
    rgb(30, 58, 138) 35%, 
    rgb(91, 33, 182) 100%
  );
  /* 防止过度滚动 */
  overscroll-behavior: none;
  -webkit-overscroll-behavior: none;
}

.glassmorphic-login-page {
  min-height: 100vh;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 1rem 4rem 1rem;
  position: relative;
  overflow: hidden;
  /* 防止过度滚动显示白色背景 */
  overscroll-behavior: none;
  -webkit-overscroll-behavior: none;
  /* 确保页面背景色覆盖整个视口 */
  background: transparent;
  /* 移动端优化 */
  -webkit-overflow-scrolling: touch;
  /* 禁用文本选择以避免滚动冲突 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 移动端特别优化 */
@supports (-webkit-appearance: none) {
  .glassmorphic-login-page {
    /* iOS Safari 特别处理 */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    padding: env(safe-area-inset-top, 1rem) 1rem env(safe-area-inset-bottom, 4rem) 1rem;
    /* 禁用弹性滚动 */
    overscroll-behavior-y: none;
    -webkit-overscroll-behavior-y: none;
  }
}

/* Beams Background */
.beams-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  pointer-events: none;
  z-index: 1;
}

.beams-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.4) 0%, 
    rgba(30, 58, 138, 0.3) 35%, 
    rgba(91, 33, 182, 0.4) 100%
  );
  backdrop-filter: blur(1px);
  pointer-events: none;
  z-index: 2;
}

/* Lanyard */
.badge-lanyard {
  position: absolute;
  top: -6rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}

.lanyard-strap {
  width: 5rem;
  height: 6rem;
  background: linear-gradient(180deg, 
    rgb(100, 116, 139) 0%, 
    rgb(71, 85, 105) 50%, 
    rgb(51, 65, 85) 100%
  );
  border-radius: 2.5rem 2.5rem 0 0;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  position: relative;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.lanyard-texture {
  position: absolute;
  inset: 0.5rem;
  background: linear-gradient(180deg, rgba(148, 163, 184, 0.2) 0%, transparent 100%);
  border-radius: 2.5rem 2.5rem 0 0;
}

.lanyard-clip {
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 3rem;
  background: linear-gradient(180deg, 
    rgba(71, 85, 105, 0.8) 0%, 
    rgba(51, 65, 85, 0.9) 50%, 
    rgba(30, 41, 59, 0.95) 100%
  );
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(71, 85, 105, 0.4);
  backdrop-filter: blur(10px);
}

.lanyard-clip-inner {
  position: absolute;
  inset: 0.25rem;
  background: linear-gradient(180deg, 
    rgba(30, 58, 138, 0.3) 0%, 
    rgba(30, 41, 59, 0.6) 100%
  );
  border-radius: 0.75rem;
  backdrop-filter: blur(5px);
}

.lanyard-clip-mechanism {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2.5rem;
  height: 0.75rem;
  background: linear-gradient(180deg, rgb(71, 85, 105) 0%, rgb(51, 65, 85) 100%);
  border-radius: 9999px;
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

/* Mobile Frame Container */
.mobile-frame {
  position: relative;
  width: 100%;
  max-width: 24rem;
  z-index: 10;
  transition: max-width 0.4s ease-in-out;
}

/* 注册模式的宽屏工牌设计 */
.mobile-frame-wide {
  max-width: 46rem; /* 宽度增加以适应横向布局 */
}

.page-indicator {
  position: absolute;
  top: -3rem;
  right: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  z-index: 20;
}

/* Badge Card */
.badge-card {
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(40px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  transition: width 0.4s ease-in-out;
}

/* 宽屏工牌卡片 */
.badge-card-wide {
  width: 100%;
}

.badge-recess {
  position: absolute;
  top: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 8rem;
  height: 4rem;
}

.recess-shadow {
  position: absolute;
  inset: 0;
  background: rgba(30, 58, 138, 0.15);
  border-radius: 50%;
  filter: blur(6px);
  backdrop-filter: blur(10px);
}

.recess-main {
  position: absolute;
  inset: 0.25rem;
  background: linear-gradient(180deg, 
    rgba(30, 58, 138, 0.2) 0%, 
    rgba(30, 58, 138, 0.1) 50%, 
    rgba(255, 255, 255, 0.05) 100%
  );
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
}

.recess-inner {
  position: absolute;
  inset: 0.5rem;
  background: linear-gradient(180deg, 
    rgba(30, 58, 138, 0.15) 0%, 
    rgba(255, 255, 255, 0.08) 100%
  );
  border-radius: 50%;
  box-shadow: inset 0 2px 4px 0 rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(15px);
}

.card-edge-lighting {
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.08) 0%, transparent 30%);
  border-radius: 2rem;
  pointer-events: none;
}

.card-content {
  position: relative;
  z-index: 10;
  padding: 2rem 2rem 1rem 2rem;
}

/* User Icon Badge */
.user-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.user-icon-badge {
  position: relative;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, 
    rgb(59, 130, 246) 0%, 
    rgb(37, 99, 235) 50%, 
    rgb(29, 78, 216) 100%
  );
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.25);
  z-index: 10;
}

.user-icon-badge svg,
.user-icon-svg {
  width: 2rem;
  height: 2rem;
  color: white;
  z-index: 20;
  position: relative;
}

.user-icon-border {
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  z-index: 5;
}

.user-icon-highlight {
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  width: 0.5rem;
  height: 0.5rem;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  filter: blur(2px);
  z-index: 15;
}

/* Form Styles */
.glassmorphic-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 注册模式下减少间距 */
.glassmorphic-login-page[data-lang] .glassmorphic-form {
  gap: 0.5rem;
}

/* 注册模式下的滚动控制 */
.glassmorphic-login-page[data-lang] {
  /* 当内容超出时允许滚动但防止过度滚动 */
  overflow-y: auto;
  overscroll-behavior-y: contain;
  /* 调整布局以适应可能的滚动 */
  align-items: flex-start;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.form-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 0.25rem;
  letter-spacing: -0.025em;
  transition: all 0.3s ease;
}

.form-subtitle {
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  min-height: 1.2rem;
  line-height: 1.2rem;
  transition: all 0.3s ease;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 进一步优化注册模式下的表单组间距 */
.glassmorphic-login-page[data-lang] .form-group {
  gap: 0.375rem;
}

.form-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.75rem 4rem 0.75rem 3rem;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 1rem;
  height: 3rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

/* 没有右侧图标的输入框 */
.form-input:not([type="password"]) {
  padding-right: 1rem;
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.form-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.form-input.error {
  border-color: rgba(248, 113, 113, 0.6);
}

.form-input.error:focus {
  border-color: rgb(248, 113, 113);
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
  width: 1.25rem;
  height: 1.25rem;
  /* Chrome兼容性修复 */
  -webkit-transform: translateY(-50%) translateZ(0);
  transform: translateY(-50%) translateZ(0);
  /* 确保在Chrome中正确显示 */
  display: flex;
  align-items: center;
  justify-content: center;
  /* 防止Chrome中的渲染问题 */
  will-change: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.input-right-element {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.password-toggle {
  color: rgba(255, 255, 255, 0.8);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  /* Chrome兼容性修复 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* 确保点击区域在Chrome中正确 */
  position: relative;
  z-index: 10;
  /* 防止Chrome中的渲染问题 */
  will-change: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 确保在Chrome中可点击 */
  -webkit-tap-highlight-color: transparent;
}

.password-toggle:hover {
  color: rgba(255, 255, 255, 1);
  /* Chrome兼容性：避免transform导致的渲染问题 */
}

.password-toggle:focus {
  outline: none;
  /* 简化focus样式，只保留颜色变化 */
  color: rgba(255, 255, 255, 1);
  /* Chrome兼容性：确保focus状态可见 */
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.password-toggle:active {
  /* 移除transform避免点击时的缩放bug */
  color: rgba(255, 255, 255, 0.9);
}

/* 眼睛图标样式 - Chrome兼容性修复 */
.eye-icon {
  /* Chrome兼容性：使用-webkit-前缀和备用方案 */
  -webkit-filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  /* Chrome渲染优化 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* 确保在Chrome中正确渲染 */
  will-change: auto;
  /* 防止Chrome中的模糊渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Chrome特定修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .eye-icon {
    /* Chrome特定的渲染优化 */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  /* Chrome中的SVG图标修复 - 解决黄色轮廓问题 */
  .input-icon svg,
  .password-toggle svg {
    /* 确保SVG在Chrome中正确渲染 */
    display: block;
    width: 100%;
    height: 100%;
    /* 强制使用正确的颜色，避免Chrome的奇怪渲染 */
    stroke: rgba(255, 255, 255, 0.6) !important;
    fill: none !important;
    color: rgba(255, 255, 255, 0.6) !important;
    /* Chrome SVG渲染优化 */
    shape-rendering: geometricPrecision;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* Chrome中的密码切换按钮特殊处理 */
  .password-toggle {
    /* 确保在Chrome中可见和可点击 */
    isolation: isolate;
  }
}

/* Enhanced Error Message Styles - 轻量化工牌风格错误提示 */
.error-message {
  color: rgb(248, 113, 113);
  background: linear-gradient(135deg, 
    rgba(248, 113, 113, 0.08) 0%,
    rgba(239, 68, 68, 0.06) 50%,
    rgba(220, 38, 38, 0.04) 100%
  );
  backdrop-filter: blur(8px) saturate(120%);
  -webkit-backdrop-filter: blur(8px) saturate(120%);
  border: 1px solid rgba(248, 113, 113, 0.2);
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  margin-top: 0.5rem;
  font-size: 0.8125rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  box-shadow: 
    0 4px 16px rgba(248, 113, 113, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 2px rgba(255, 255, 255, 0.08);
  animation: errorSlideIn 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  max-height: 2.5rem;
}

/* 错误消息图标 - 更小更精致 */
.error-message::before {
  content: '⚠️';
  font-size: 0.875rem;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  flex-shrink: 0;
}

/* 错误消息动画 - 更轻柔 */
@keyframes errorSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    max-height: 2.5rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}

/* 针对不同类型的错误消息 - 保持轻量 */
.error-message.critical {
  background: linear-gradient(135deg, 
    rgba(239, 68, 68, 0.12) 0%,
    rgba(220, 38, 38, 0.08) 50%,
    rgba(185, 28, 28, 0.06) 100%
  );
  border-color: rgba(239, 68, 68, 0.25);
  color: rgb(252, 165, 165);
}

.error-message.warning {
  background: linear-gradient(135deg, 
    rgba(251, 191, 36, 0.08) 0%,
    rgba(245, 158, 11, 0.06) 50%,
    rgba(217, 119, 6, 0.04) 100%
  );
  border-color: rgba(251, 191, 36, 0.2);
  color: rgb(251, 191, 36);
}

.error-message.warning::before {
  content: '⚠️';
}

/* 表单提交错误消息特殊样式 - 减少动画强度 */
.submit-error-message {
  margin-top: 0.75rem;
  margin-bottom: 0.25rem;
  animation: errorSlideIn 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 移除过强的脉冲动画 */
@keyframes errorPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.005);
  }
}

/* Loading animations removed */

/* 成功状态样式 - 轻量化设计 */
.success-message {
  color: rgb(34, 197, 94);
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.08) 0%,
    rgba(22, 163, 74, 0.06) 50%,
    rgba(21, 128, 61, 0.04) 100%
  );
  backdrop-filter: blur(8px) saturate(120%);
  -webkit-backdrop-filter: blur(8px) saturate(120%);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  margin-top: 0.5rem;
  font-size: 0.8125rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  box-shadow: 
    0 4px 16px rgba(34, 197, 94, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 2px rgba(255, 255, 255, 0.08);
  animation: successSlideIn 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  max-height: 2.5rem;
}

.success-message::before {
  content: '✅';
  font-size: 0.875rem;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  flex-shrink: 0;
}

@keyframes successSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    max-height: 2.5rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}

/* Loading styles removed */

/* 错误消息响应式优化 - 进一步减少尺寸 */
@media (max-width: 375px) {
  .error-message {
    font-size: 0.6875rem;
    padding: 0.25rem 0.5rem;
    margin-top: 0.25rem;
    max-height: 1.5rem;
  }
  
  .error-message::before {
    font-size: 0.75rem;
  }
  
  .submit-error-message {
    margin-top: 0.5rem;
    margin-bottom: 0.125rem;
  }
}

/* 成功消息响应式优化 */
@media (max-width: 480px) {
  .success-message {
    font-size: 0.75rem;
    padding: 0.375rem 0.625rem;
    margin-top: 0.375rem;
    max-height: 2rem;
  }
  
  .success-message::before {
    font-size: 0.75rem;
  }
}

/* Loading styles removed */

/* 工牌紧凑性保持 - 确保错误消息不破坏布局 */
.glassmorphic-form {
  position: relative;
}

.glassmorphic-form .error-message,
.glassmorphic-form .success-message {
  position: relative;
  z-index: 1;
}

/* Loading animations removed */

/* 加载状态下按钮的特殊样式确保不改变外观 */
.btn-primary:disabled {
  opacity: 0.85 !important;
  cursor: not-allowed !important;
  transform: none !important;
  /* 保持工牌质感 */
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.35) 0%,
    rgba(139, 92, 246, 0.3) 50%,
    rgba(59, 130, 246, 0.25) 100%
  ) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  box-shadow: 
    0 8px 28px rgba(99, 102, 241, 0.15),
    0 4px 14px rgba(139, 92, 246, 0.1),
    0 2px 7px rgba(0, 0, 0, 0.08),
    inset 0 2px 4px rgba(255, 255, 255, 0.15),
    inset 0 -2px 3px rgba(99, 102, 241, 0.1) !important;
}

.btn-secondary:disabled {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
  transform: none !important;
  /* 保持工牌质感 */
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.03) 100%
  ) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

/* StarBorder 在禁用状态下的优化 */
.star-border-primary:disabled {
  opacity: 0.85 !important;
  cursor: not-allowed !important;
  pointer-events: none;
}

.star-border-secondary:disabled {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
  pointer-events: none;
}

/* 确保加载状态不会触发hover效果 */
.btn-primary:disabled:hover,
.btn-secondary:disabled:hover {
  transform: none !important;
  background: initial !important;
  border-color: initial !important;
  box-shadow: initial !important;
}

/* 移除禁用状态下的按钮尺寸覆盖，使用原有的最小高度 */
.btn-primary:disabled,
.btn-secondary:disabled,
.star-border-primary:disabled,
.star-border-secondary:disabled {
  min-height: 3.5rem;
  height: auto; /* 改为auto以适应内容 */
  width: 100%;
}

/* Spin animation removed */

/* Divider */
.form-divider {
  display: flex;
  align-items: center;
  margin: 0.75rem 0;
}

/* 优化注册模式下的分隔线间距 */
.glassmorphic-login-page[data-lang] .form-divider {
  margin: 0.5rem 0;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
}

.divider-text {
  padding: 0 1.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Social Login - 重新设计，减少拥挤 */
.social-login {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-top: 1rem;
  padding: 0 1rem;
}

/* 优化注册模式下的社交登录区域间距 */
.glassmorphic-login-page[data-lang] .social-login {
  gap: 1.25rem;
  margin-top: 0.75rem;
}

.social-button {
  position: relative;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.12) 50%,
    rgba(255, 255, 255, 0.08) 100%
  );
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 1.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  color: white;
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(255, 255, 255, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 2px 4px rgba(255, 255, 255, 0.2),
    inset 0 -1px 2px rgba(255, 255, 255, 0.08);
}

/* 社交按钮流光效果 */
.social-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transition: left 0.8s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 1;
}

/* 社交按钮内容层 */
.social-button svg {
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.social-button:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.22) 0%,
    rgba(255, 255, 255, 0.18) 50%,
    rgba(255, 255, 255, 0.14) 100%
  );
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-4px) scale(1.1);
  box-shadow: 
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(255, 255, 255, 0.12),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 3px 6px rgba(255, 255, 255, 0.3),
    inset 0 -1px 3px rgba(255, 255, 255, 0.15);
}

.social-button:hover::before {
  left: 100%;
}

.social-button:hover svg {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
}

.social-button:active {
  transform: translateY(-1px) scale(1.05);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.11) 50%,
    rgba(255, 255, 255, 0.08) 100%
  );
  box-shadow: 
    0 6px 18px rgba(0, 0, 0, 0.1),
    inset 0 2px 4px rgba(255, 255, 255, 0.15);
}

.social-button:active svg {
  transform: scale(1.05);
}

.social-button svg {
  width: 1.75rem;
  height: 1.75rem;
  color: white;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

/* Form Switch */
.form-switch {
  margin-top: 1rem;
  text-align: center;
}

.switch-button {
  color: rgba(255, 255, 255, 0.7);
  background: none;
  border: none;
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.switch-button:hover {
  color: white;
}

/* Language Select */
.language-select {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 1rem;
  height: 3rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.language-select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.language-select option {
  background: rgb(30, 41, 59);
  color: white;
}

/* Floating Navigation - 优化的对称布局 */
.floating-nav-buttons {
  position: fixed;
  top: 1.5rem;
  left: 1.5rem;
  right: 1.5rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-areas: "left right";
  align-items: start;
  z-index: 1000;
  pointer-events: none;
}

.floating-nav-buttons > * {
  pointer-events: all;
}

.back-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.language-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* 左上角返回按钮 */
.floating-back-button {
  grid-area: left;
  justify-self: start;
  align-self: start;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow:
    inset 0 1px 2px rgba(255, 255, 255, 0.35),
    0 4px 20px rgba(0, 0, 0, 0.25);
  border-radius: 2rem;
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  min-width: 140px;
}

.floating-back-button::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.05) 100%);
  mix-blend-mode: overlay;
  pointer-events: none;
}

.floating-back-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow:
    inset 0 1px 2px rgba(255, 255, 255, 0.45),
    0 8px 24px rgba(0, 0, 0, 0.35);
}

/* 右上角语言下拉菜单 */
/* 简化的语言切换按钮 */
.floating-language-simple {
  grid-area: right;
  justify-self: end;
  align-self: start;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 1.25rem;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow:
    inset 0 1px 2px rgba(255, 255, 255, 0.35),
    0 4px 20px rgba(0, 0, 0, 0.25);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.025em;
}

.floating-language-simple::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  pointer-events: none; /* 彻底禁用伪元素的交互 */
}

.floating-language-simple:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    inset 0 2px 4px rgba(255, 255, 255, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(255, 255, 255, 0.1);
}

.floating-language-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow:
    inset 0 1px 2px rgba(255, 255, 255, 0.35),
    0 4px 20px rgba(0, 0, 0, 0.25);
  border-radius: 2rem;
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  min-width: 100px;
  justify-content: space-between;
}

.floating-language-toggle::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.05) 100%);
  mix-blend-mode: overlay;
  pointer-events: none;
}

.floating-language-toggle:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow:
    inset 0 1px 2px rgba(255, 255, 255, 0.45),
    0 8px 24px rgba(0, 0, 0, 0.35);
}

.dropdown-arrow {
  transition: transform 0.2s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.2s ease;
  width: 100%;
  text-align: left;
}

.language-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.language-option.active {
  background: rgba(59, 130, 246, 0.2);
}

/* MacBook 14寸优化 - 确保内容完全可见 */
@media screen and (min-width: 1440px) and (max-height: 932px) {
  .glassmorphic-login-page {
    padding: 0.5rem 1rem 1rem 1rem;
  }

  .card-content {
    padding: 1.5rem 2rem 0.75rem 2rem;
  }

  .user-icon-container {
    margin-bottom: 0.75rem;
  }

  .form-subtitle {
    margin-bottom: 0.75rem;
  }

  .glassmorphic-form {
    gap: 0.5rem;
  }

  .form-group {
    gap: 0.375rem;
  }

  .form-buttons {
    gap: 0.5rem;
    margin-top: 0.75rem;
  }

  .form-divider {
    margin: 0.5rem 0;
  }

  .social-login {
    gap: 0.75rem;
    margin-top: 0.5rem;
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  /* 移动端滑动白边修复 */
  html, body {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
    overscroll-behavior: none;
    -webkit-overscroll-behavior: none;
  }
  
  .glassmorphic-login-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    padding: 0.5rem 0.5rem 3rem 0.5rem;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    align-items: center;
    /* 移动端防止过度滚动 */
    overscroll-behavior: none;
    -webkit-overscroll-behavior: none;
    /* 移动端触摸滚动优化 */
    -webkit-overflow-scrolling: touch;
    /* 防止弹性滚动显示白边 */
    scroll-behavior: smooth;
    /* 禁用用户缩放 */
    touch-action: pan-y;
  }
  
  /* 移动端注册模式的滚动优化 */
  .glassmorphic-login-page[data-lang] {
    padding-top: 1rem;
    padding-bottom: 1rem;
    align-items: flex-start;
    justify-content: flex-start;
    /* 注册模式下确保内容可滚动 */
    height: auto;
    min-height: 100vh;
  }
  
  .mobile-frame {
    max-width: 100%;
    width: 100%;
  }
  
  .badge-lanyard {
    top: -4rem;
  }
  
  .lanyard-strap {
    width: 4rem;
    height: 5rem;
  }
  
  .card-content {
    padding: 2.5rem 1.5rem 1.25rem 1.5rem;
  }
  
  .page-indicator {
    top: -2.5rem;
  }
  
  .floating-nav-buttons {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .floating-back-button {
    min-width: 120px;
    padding: 0.625rem 0.875rem;
    font-size: 0.8125rem;
  }
  
  .floating-language-toggle {
    min-width: 85px;
    padding: 0.625rem 0.875rem;
    font-size: 0.8125rem;
  }
  
  .back-icon {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
  }
  
  .form-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .form-subtitle {
    margin-bottom: 1rem;
    font-size: 0.8rem;
  }
  
  .form-group {
    gap: 0.5rem;
  }
  
  .glassmorphic-form {
    gap: 1rem;
  }
  
  .form-input {
    height: 3rem;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  }
  
  .input-icon {
    left: 0.75rem;
    width: 1rem;
    height: 1rem;
  }
  
  .input-right-element {
    right: 0.75rem;
  }
  
  .password-toggle {
    width: 2.25rem;
    height: 2.25rem;
    padding: 0.375rem;
  }
  
  .form-buttons {
    flex-direction: column;
    gap: 1rem;
    padding-top: 1rem;
  }
  
  .btn-primary, .btn-secondary {
    min-height: 3.5rem;
    padding: 1rem 1.5rem;
    border-radius: 1rem;
    font-size: 1rem;
    font-weight: 600;
  }

  .btn-primary {
    min-height: 3.75rem;
    font-size: 1.05rem;
    font-weight: 700;
  }
  
  .form-divider {
    margin: 1rem 0;
  }
  
    .social-login {
    gap: 1rem;
    margin-top: 0.75rem;
    padding: 0 0.5rem;
  }

  .social-button {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 1.25rem;
  }

  .social-button svg {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .user-icon-container {
    margin-bottom: 1.25rem;
  }
  
  .user-icon-badge {
    width: 3.5rem;
    height: 3.5rem;
  }
  
  .user-icon-badge svg {
    width: 1.75rem;
    height: 1.75rem;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  /* 超小屏幕滑动白边修复 */
  html, body {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
    overscroll-behavior: none;
    -webkit-overscroll-behavior: none;
    /* 禁用 iOS Safari 弹性滚动 */
    -webkit-overflow-scrolling: auto;
  }
  
  .glassmorphic-login-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    padding: 0.5rem 0.5rem 3.5rem 0.5rem;
    overflow-y: auto;
    overflow-x: hidden;
    /* 超小屏幕防止过度滚动 */
    overscroll-behavior: none;
    -webkit-overscroll-behavior: none;
    /* 触摸滚动优化 */
    -webkit-overflow-scrolling: touch;
    /* 防止用户缩放 */
    touch-action: pan-y;
  }
  
  /* 超小屏幕注册模式的滚动优化 */
  .glassmorphic-login-page[data-lang] {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    height: auto;
    min-height: 100vh;
  }
  
  .floating-nav-buttons {
    top: 0.75rem;
    left: 0.75rem;
    right: 0.75rem;
    gap: 0.5rem;
  }
  
  .floating-back-button {
    min-width: 100px;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    gap: 0.375rem;
  }
  
  .floating-language-simple {
    min-width: 75px;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    gap: 0.375rem;
    border-radius: 1rem;
  }
  
  .back-icon,
  .language-icon {
    width: 0.875rem;
    height: 0.875rem;
  }
  
  .dropdown-arrow {
    font-size: 0.625rem;
  }
}

/* Login Page Specific Overrides to enhance readability */
.glassmorphic-login-page .glassmorphic-form .form-input {
  background: rgba(255, 255, 255, 0.18) !important; /* white translucent background */
  color: #ffffff !important; /* ensure input text is white */
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
}

.glassmorphic-login-page .glassmorphic-form .form-input:focus {
  background: rgba(255, 255, 255, 0.28) !important;
  border-color: rgba(255, 255, 255, 0.6) !important;
}

.glassmorphic-login-page .glassmorphic-form .form-input::placeholder {
  color: rgba(255, 255, 255, 0.75) !important; /* brighter placeholder */
}

.glassmorphic-login-page .glassmorphic-form .form-label {
  color: rgba(255, 255, 255, 0.95) !important; /* ensure labels are bright */
}

.glassmorphic-login-page .glassmorphic-form .input-icon,
.glassmorphic-login-page .glassmorphic-form .password-toggle {
  color: rgba(255, 255, 255, 0.8) !important; /* brighter icons */
}

/* Chrome特定的SVG图标修复 - 解决黄色轮廓问题 */
.glassmorphic-login-page .glassmorphic-form .input-icon svg,
.glassmorphic-login-page .glassmorphic-form .password-toggle svg {
  /* 强制使用白色，避免Chrome的颜色继承问题 */
  color: rgba(255, 255, 255, 0.8) !important;
  fill: none !important;
  stroke: rgba(255, 255, 255, 0.8) !important;
  /* Chrome渲染优化 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* 确保SVG正确显示 */
  display: block;
  width: 100%;
  height: 100%;
}

/* Chrome浏览器特定修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .glassmorphic-login-page .glassmorphic-form .input-icon svg,
  .glassmorphic-login-page .glassmorphic-form .password-toggle svg {
    /* 强制覆盖Chrome的默认SVG样式 */
    stroke: rgba(255, 255, 255, 0.8) !important;
    fill: none !important;
    color: rgba(255, 255, 255, 0.8) !important;
    /* 防止Chrome的奇怪渲染 */
    shape-rendering: geometricPrecision;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}

/* 额外的Chrome修复 - 针对黄色轮廓问题 */
@supports (-webkit-appearance: none) {
  /* 强制重置所有可能导致颜色问题的属性 */
  .input-icon svg *,
  .password-toggle svg * {
    stroke: rgba(255, 255, 255, 0.8) !important;
    fill: none !important;
    color: rgba(255, 255, 255, 0.8) !important;
  }

  /* 确保父容器的颜色正确传递 */
  .input-icon,
  .password-toggle {
    color: rgba(255, 255, 255, 0.8) !important;
    -webkit-text-fill-color: rgba(255, 255, 255, 0.8) !important;
  }
}

/* 最终的Chrome兼容性修复 - 覆盖所有可能的问题 */
.input-icon svg,
.password-toggle svg,
.input-icon svg path,
.password-toggle svg path,
.input-icon svg circle,
.password-toggle svg circle,
.input-icon svg rect,
.password-toggle svg rect,
.input-icon svg line,
.password-toggle svg line,
.input-icon svg polyline,
.password-toggle svg polyline {
  stroke: rgba(255, 255, 255, 0.8) !important;
  fill: none !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 额外的滑动白边防护 - 确保所有层级都有背景色 */
*, *::before, *::after {
  box-sizing: border-box;
}

/* 为移动端Safari特别处理 */
@supports (-webkit-appearance: none) {
  html {
    background: linear-gradient(135deg, 
      rgb(15, 23, 42) 0%, 
      rgb(30, 58, 138) 35%, 
      rgb(91, 33, 182) 100%
    ) !important;
    background-attachment: fixed !important;
  }
  
  body {
    background: linear-gradient(135deg, 
      rgb(15, 23, 42) 0%, 
      rgb(30, 58, 138) 35%, 
      rgb(91, 33, 182) 100%
    ) !important;
    background-attachment: fixed !important;
  }
}

/* iOS 特殊处理 */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .glassmorphic-login-page {
    /* 禁用iOS的弹性滚动 */
    -webkit-overflow-scrolling: auto !important;
    overscroll-behavior-y: none !important;
    overscroll-behavior-x: none !important;
  }
}

/* 防止任何可能的白边 */
html::before,
body::before,
#root::before {
  content: '';
  position: fixed;
  top: -100vh;
  left: -100vw;
  width: 300vw;
  height: 300vh;
  background: linear-gradient(135deg, 
    rgb(15, 23, 42) 0%, 
    rgb(30, 58, 138) 35%, 
    rgb(91, 33, 182) 100%
  );
  z-index: -1000;
  pointer-events: none;
}

/* 注册表单的横向布局 */
.badge-header {
  width: 100%;
  margin-bottom: 1.5rem;
}

.register-form-horizontal {
  width: 100%;
}

.register-form-columns {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.register-form-column {
  flex: 1;
  min-width: 0; /* 确保flex项目可以缩小到其内容宽度以下 */
}

.register-form-footer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 2rem;
}

/* 水平社交登录区域 */
.social-login.horizontal {
  flex-direction: row;
  align-items: center;
  margin-top: 0;
  flex: 1;
}

.social-buttons {
  display: flex;
  gap: 0.75rem;
}

.form-divider.horizontal {
  margin: 0 1.5rem 0 0;
  flex-direction: row;
  align-items: center;
  gap: 0.75rem;
  width: auto;
}

.form-divider.horizontal .divider-line {
  width: 2rem;
  height: 1px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  /* 在小屏幕上恢复垂直布局 */
  .mobile-frame-wide {
    max-width: 24rem;
  }
  
  .register-form-columns {
    flex-direction: column;
    gap: 1rem;
  }
  
  .register-form-footer {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .social-login.horizontal {
    flex-direction: column;
    width: 100%;
  }
  
  .form-divider.horizontal {
    width: 100%;
    margin: 1rem 0;
    flex-direction: row;
  }
  
  .social-buttons {
    width: 100%;
    justify-content: center;
  }
}

/* StarBorder Button Styles */
.star-border-primary,
.star-border-secondary {
  border: none;
  background: transparent;
  cursor: pointer;
  width: 100%;
  margin: 0;
  font-family: inherit;
}

.star-border-primary:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.star-border-primary .inner-content,
.star-border-secondary .inner-content {
  background: rgba(255, 255, 255, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    inset 0 0.5px 0 rgba(255, 255, 255, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.08);
}

.star-border-primary .inner-content {
  /* 与界面其他按钮保持一致的样式 */
}

.star-border-secondary .inner-content {
  /* 与界面其他按钮保持一致的样式 */
}

.star-border-primary:hover:not(:disabled) .inner-content {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 
    inset 0 0.5px 0 rgba(255, 255, 255, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.12);
}

.star-border-secondary:hover .inner-content {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 
    inset 0 0.5px 0 rgba(255, 255, 255, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.12);
}

.star-border-primary:active:not(:disabled) .inner-content {
  transform: translateY(0px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 
    inset 0 0.5px 0 rgba(255, 255, 255, 0.18),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

.star-border-secondary:active .inner-content {
  transform: translateY(0px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 
    inset 0 0.5px 0 rgba(255, 255, 255, 0.18),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Loading state for StarBorder buttons */
.star-border-primary:disabled .inner-content {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: none;
}

.star-border-primary:disabled,
.star-border-secondary:disabled {
  opacity: 0.7;
}

/* Enhanced loading spinner for StarBorder buttons removed */

/* StarBorder loading animations removed */

/* Responsive adjustments for StarBorder buttons */
@media (max-width: 640px) {
  .star-border-primary .inner-content,
  .star-border-secondary .inner-content {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .star-border-primary:hover:not(:disabled) .inner-content,
  .star-border-secondary:hover .inner-content {
    transform: translateY(-1px);
  }
}

@media (max-width: 480px) {
  .star-border-primary .inner-content,
  .star-border-secondary .inner-content {
    padding: 9px 14px;
    font-size: 13px;
  }
  
  .star-border-primary:hover:not(:disabled) .inner-content,
  .star-border-secondary:hover .inner-content {
    transform: translateY(-1px);
    background: rgba(255, 255, 255, 0.22);
    border-color: rgba(255, 255, 255, 0.45);
    box-shadow: 
      inset 0 0.5px 0 rgba(255, 255, 255, 0.18),
      0 3px 10px rgba(0, 0, 0, 0.1);
  }
}

/* Register Footer button width adjustment */
.register-form-footer .form-buttons {
  flex: 0 0 40%; /* 占用可视宽度 40%，避免过窄 */
}

/* Reduce gap between buttons and OR section */
.register-form-footer {
  gap: 1.25rem;
}

/* 修复语言切换按钮悬停闪烁问题 */
.floating-language-simple::before {
  pointer-events: none;
}

/* Small loading animation styles removed */

/* Password Strength */
.password-strength {
  margin-top: 0.5rem;
}

/* 优化注册模式下的密码强度指示器间距 */
.glassmorphic-login-page[data-lang] .password-strength {
  margin-top: 0.25rem;
}

.password-strength-bar {
  height: 2px;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.password-strength.weak .password-strength-bar {
  width: 33%;
  background: rgb(248, 113, 113);
}

.password-strength.medium .password-strength-bar {
  width: 66%;
  background: rgb(251, 191, 36);
}

.password-strength.strong .password-strength-bar {
  width: 100%;
  background: rgb(34, 197, 94);
}

.password-strength-text {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.75rem 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 0.5rem;
  accent-color: rgb(59, 130, 246);
}

.forgot-password {
  color: rgba(255, 255, 255, 0.7);
  background: none;
  border: none;
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.forgot-password:hover {
  color: white;
}

/* Action Buttons - 增强的流动玻璃质感与优雅渐变 */
.form-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

/* 优化注册模式下的按钮区域间距 */
.glassmorphic-login-page[data-lang] .form-buttons {
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.btn-primary,
.btn-secondary {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 1rem 1.5rem;
  border-radius: 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  min-height: 3rem;
  letter-spacing: 0.025em;
}

/* 主要按钮 - 登录/注册 - 工牌风格增强 */
.btn-primary {
  color: white;
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.4) 0%,
    rgba(139, 92, 246, 0.35) 50%,
    rgba(59, 130, 246, 0.3) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 12px 40px rgba(99, 102, 241, 0.2),
    0 6px 20px rgba(139, 92, 246, 0.15),
    0 3px 10px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.25),
    inset 0 -2px 3px rgba(99, 102, 241, 0.15);
  min-height: 3.5rem;
  font-size: 1.1rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  border-radius: 1.25rem;
  position: relative;
  overflow: hidden;
}

/* 主要按钮光晕效果层 */
.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.25) 50%,
    transparent 100%
  );
  transition: left 0.8s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 1;
}

/* 主要按钮内容层 */
.btn-primary > * {
  position: relative;
  z-index: 2;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.55) 0%,
    rgba(139, 92, 246, 0.5) 50%,
    rgba(59, 130, 246, 0.45) 100%
  );
  border-color: rgba(255, 255, 255, 0.45);
  transform: translateY(-4px) scale(1.03);
  box-shadow: 
    0 20px 60px rgba(99, 102, 241, 0.3),
    0 10px 30px rgba(139, 92, 246, 0.25),
    0 5px 15px rgba(0, 0, 0, 0.2),
    inset 0 3px 6px rgba(255, 255, 255, 0.4),
    inset 0 -2px 4px rgba(99, 102, 241, 0.2);
}

.btn-primary:hover:not(:disabled)::before {
  left: 100%;
}

.btn-primary:active {
  transform: translateY(-1px) scale(1.01);
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.35) 0%,
    rgba(139, 92, 246, 0.3) 50%,
    rgba(99, 102, 241, 0.25) 100%
  );
  box-shadow: 
    0 8px 24px rgba(99, 102, 241, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 2px 4px rgba(99, 102, 241, 0.2);
}

/* 次要按钮 - 切换模式 - 工牌风格增强 */
.btn-secondary {
  color: rgba(255, 255, 255, 0.9);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.06) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 28px rgba(0, 0, 0, 0.1),
    0 4px 14px rgba(255, 255, 255, 0.08),
    0 2px 7px rgba(0, 0, 0, 0.06),
    inset 0 2px 3px rgba(255, 255, 255, 0.15),
    inset 0 -1px 2px rgba(255, 255, 255, 0.08);
  min-height: 3.25rem;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.025em;
  border-radius: 1.25rem;
}

/* 次要按钮光晕效果层 */
.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.15) 50%,
    transparent 100%
  );
  transition: left 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 1;
}

/* 次要按钮内容层 */
.btn-secondary > * {
  position: relative;
  z-index: 2;
}

.btn-secondary:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.12) 50%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px) scale(1.01);
  box-shadow: 
    0 12px 36px rgba(0, 0, 0, 0.12),
    0 6px 18px rgba(255, 255, 255, 0.05),
    0 3px 9px rgba(0, 0, 0, 0.08),
    inset 0 1px 2px rgba(255, 255, 255, 0.2),
    inset 0 -1px 1px rgba(255, 255, 255, 0.08);
}

.btn-secondary:hover::before {
  left: 100%;
}

.btn-secondary:active {
  transform: translateY(-1px) scale(1.005);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.07) 50%,
    rgba(255, 255, 255, 0.04) 100%
  );
  box-shadow: 
    0 6px 18px rgba(0, 0, 0, 0.1),
    inset 0 2px 3px rgba(255, 255, 255, 0.1);
}